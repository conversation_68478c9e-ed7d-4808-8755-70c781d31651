import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:logic_lab/data/models/puzzle.dart';

/// 测试镜像对称游戏修复效果
void main() async {
  print('🧪 开始测试镜像对称游戏修复效果...\n');
  
  try {
    // 1. 测试JSON数据结构
    await testJsonDataStructure();
    
    // 2. 测试答案验证逻辑
    await testAnswerValidation();
    
    print('\n✅ 所有测试通过！镜像对称游戏修复成功！');
  } catch (e) {
    print('\n❌ 测试失败: $e');
  }
}

/// 测试JSON数据结构
Future<void> testJsonDataStructure() async {
  print('📋 测试1: JSON数据结构');
  
  try {
    // 读取修复后的JSON文件
    final jsonString = await rootBundle.loadString('assets/puzzles/mirror_symmetry_sample.json');
    final jsonData = json.decode(jsonString);
    
    // 验证基本结构
    assert(jsonData['levelId'] == 'mirror_symmetry_sample', '❌ levelId不正确');
    assert(jsonData['puzzleType'] == 'MIRROR_SYMMETRY', '❌ puzzleType不正确');
    
    // 验证data字段结构
    final data = jsonData['data'];
    assert(data != null, '❌ data字段缺失');
    assert(data['originalImage'] != null, '❌ originalImage字段缺失');
    assert(data['mirrorDirection'] != null, '❌ mirrorDirection字段缺失');
    assert(data['originalPattern'] != null, '❌ originalPattern字段缺失');
    assert(data['options'] != null, '❌ options字段缺失');
    assert(data['answer'] != null, '❌ answer字段缺失');
    
    // 验证选项数据
    final options = data['options'] as List;
    assert(options.length == 4, '❌ 选项数量不正确，应该是4个');
    
    // 验证正确答案
    final correctAnswer = data['answer'];
    assert(correctAnswer == 'green_tshirt_heart_center', '❌ 正确答案不正确');
    
    // 验证选项内容
    final expectedOptions = [
      'blue_tshirt_heart_left_bow',
      'yellow_tshirt_heart_right_necklace', 
      'pink_tshirt_bow_left_necklace',
      'green_tshirt_heart_center'
    ];
    
    for (int i = 0; i < expectedOptions.length; i++) {
      assert(options[i] == expectedOptions[i], '❌ 选项${i+1}不正确: ${options[i]} != ${expectedOptions[i]}');
    }
    
    print('  ✅ JSON数据结构正确');
    print('  ✅ 包含所有必需字段');
    print('  ✅ 选项数据正确');
    print('  ✅ 正确答案设置正确');
    
  } catch (e) {
    throw Exception('JSON数据结构测试失败: $e');
  }
}

/// 测试答案验证逻辑
Future<void> testAnswerValidation() async {
  print('\n📋 测试2: 答案验证逻辑');
  
  try {
    // 模拟JSON数据
    final testData = {
      'originalImage': 'pink_tshirt_heart_right',
      'mirrorDirection': 'horizontal',
      'originalPattern': {
        'type': 'heart',
        'position': 'right',
        'color': 'pink'
      },
      'options': [
        'blue_tshirt_heart_left_bow',
        'yellow_tshirt_heart_right_necklace',
        'pink_tshirt_bow_left_necklace',
        'green_tshirt_heart_center'
      ],
      'answer': 'green_tshirt_heart_center',
      'explanation': '在镜像对称中,只有位于中心的图案在镜子中看起来是完全一样的。'
    };
    
    // 创建MirrorSymmetryData对象
    final mirrorData = MirrorSymmetryData.fromJson(testData);
    
    // 测试正确答案
    final correctAnswer = 'green_tshirt_heart_center';
    assert(mirrorData.isCorrectAnswer(correctAnswer), '❌ 正确答案验证失败');
    print('  ✅ 正确答案验证通过: $correctAnswer');
    
    // 测试错误答案
    final wrongAnswers = [
      'blue_tshirt_heart_left_bow',
      'yellow_tshirt_heart_right_necklace',
      'pink_tshirt_bow_left_necklace'
    ];
    
    for (final wrongAnswer in wrongAnswers) {
      assert(!mirrorData.isCorrectAnswer(wrongAnswer), '❌ 错误答案应该被识别为错误: $wrongAnswer');
      print('  ✅ 错误答案正确识别: $wrongAnswer');
    }
    
    // 验证数据字段
    assert(mirrorData.originalImage == 'pink_tshirt_heart_right', '❌ originalImage不正确');
    assert(mirrorData.mirrorDirection == 'horizontal', '❌ mirrorDirection不正确');
    assert(mirrorData.answer == 'green_tshirt_heart_center', '❌ answer不正确');
    assert(mirrorData.options.length == 4, '❌ options数量不正确');
    
    print('  ✅ MirrorSymmetryData对象创建成功');
    print('  ✅ 所有字段验证通过');
    
  } catch (e) {
    throw Exception('答案验证逻辑测试失败: $e');
  }
}
