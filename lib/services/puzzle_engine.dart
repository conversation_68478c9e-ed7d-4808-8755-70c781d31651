import 'dart:convert';
import 'dart:math';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import '../data/models/puzzle.dart';
import '../domain/services/answer_explanation_factory.dart';
import '../core/constants/app_constants.dart';
import '../domain/services/skill_mapping_service.dart';

import '../data/converters/puzzle_type_converter.dart';

/// 谜题引擎 - 核心游戏逻辑处理
class PuzzleEngine {
  static final PuzzleEngine _instance = PuzzleEngine._internal();
  factory PuzzleEngine() => _instance;
  PuzzleEngine._internal();

  final Logger _logger = Logger();
  final Map<String, Puzzle> _puzzleCache = {};
  final Map<String, List<String>> _worldPuzzles = {};
  bool _initialized = false;

  /// 初始化谜题引擎
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      await _loadAllPuzzles();
      _initialized = true;
      _logger.i('PuzzleEngine initialized successfully');
    } catch (e, stackTrace) {
      _logger.e(
        'Failed to initialize PuzzleEngine',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 加载所有谜题
  Future<void> _loadAllPuzzles() async {
    try {
      // 加载谜题索引文件
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = json.decode(manifestContent);

      // 找到所有谜题文件
      final puzzleFiles = manifestMap.keys
          .where(
            (key) => key.startsWith('assets/puzzles/') && key.endsWith('.json'),
          )
          .toList();

      _logger.i('Found ${puzzleFiles.length} puzzle files');

      // 加载每个谜题文件
      for (final filePath in puzzleFiles) {
        try {
          final content = await rootBundle.loadString(filePath);
          final puzzleData = json.decode(content) as Map<String, dynamic>;

          final puzzle = Puzzle.fromJson(puzzleData);
          _puzzleCache[puzzle.levelId] = puzzle;

          // 按主题世界分组
          final worldId = puzzle.themeWorld?.id ?? 'default';
          _worldPuzzles.putIfAbsent(worldId, () => []);
          _worldPuzzles[worldId]!.add(puzzle.levelId);

          _logger.d('Loaded puzzle: ${puzzle.levelId}');
        } catch (e) {
          _logger.w('Failed to load puzzle from $filePath: $e');
        }
      }

      // 对每个世界的谜题按顺序排序
      for (final worldId in _worldPuzzles.keys) {
        _worldPuzzles[worldId]!.sort((a, b) {
          final puzzleA = _puzzleCache[a]!;
          final puzzleB = _puzzleCache[b]!;

          // 先按orderInWorld排序，再按难度排序
          final orderA = puzzleA.orderInWorld ?? 999;
          final orderB = puzzleB.orderInWorld ?? 999;

          if (orderA != orderB) {
            return orderA.compareTo(orderB);
          }

          return puzzleA.difficulty.level.compareTo(puzzleB.difficulty.level);
        });
      }

      _logger.i(
        'Loaded ${_puzzleCache.length} puzzles across ${_worldPuzzles.length} worlds',
      );
    } catch (e, stackTrace) {
      _logger.e('Failed to load puzzles', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  /// 根据ID获取谜题
  Puzzle? getPuzzle(String levelId) {
    if (!_initialized) {
      _logger.w('PuzzleEngine not initialized');
      return null;
    }
    return _puzzleCache[levelId];
  }

  /// 加载谜题（异步版本，为了与Repository接口兼容）
  Future<Puzzle?> loadPuzzle(String levelId) async {
    return getPuzzle(levelId);
  }

  /// 获取所有谜题
  Future<List<Puzzle>> getAllPuzzles() async {
    if (!_initialized) {
      _logger.w('PuzzleEngine not initialized');
      return [];
    }
    return _puzzleCache.values.toList();
  }

  /// 获取推荐谜题
  Future<List<Puzzle>> getRecommendedPuzzles({
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
    int limit = 10,
  }) async {
    if (!_initialized) {
      _logger.w('PuzzleEngine not initialized');
      return [];
    }

    final allPuzzles = _puzzleCache.values.toList();
    final incompletePuzzles = allPuzzles
        .where((puzzle) => !completedLevels.contains(puzzle.levelId))
        .toList();

    // 根据用户技能水平推荐合适的谜题
    final suitablePuzzles = incompletePuzzles.where((puzzle) {
      final domainPuzzleType = PuzzleTypeConverter.coreToDomain(
        puzzle.puzzleType,
      );
      final skillType = SkillMappingService.getSkillTypeFromPuzzleType(
        domainPuzzleType,
      );
      final userLevel = (userSkillPoints[skillType.id] ?? 0) ~/ 100 + 1;

      // 推荐稍有挑战性但不会太难的谜题
      switch (puzzle.difficulty) {
        case DifficultyLevel.easy:
          return userLevel <= 3;
        case DifficultyLevel.medium:
          return userLevel >= 2 && userLevel <= 5;
        case DifficultyLevel.hard:
          return userLevel >= 4 && userLevel <= 8;
        case DifficultyLevel.expert:
          return userLevel >= 6;
      }
    }).toList();

    // 按推荐优先级排序
    suitablePuzzles.sort((a, b) {
      // 优先推荐用户擅长的类型
      final domainTypeA = PuzzleTypeConverter.coreToDomain(a.puzzleType);
      final domainTypeB = PuzzleTypeConverter.coreToDomain(b.puzzleType);
      final skillTypeA = SkillMappingService.getSkillTypeFromPuzzleType(
        domainTypeA,
      );
      final skillTypeB = SkillMappingService.getSkillTypeFromPuzzleType(
        domainTypeB,
      );
      final skillA = userSkillPoints[skillTypeA.id] ?? 0;
      final skillB = userSkillPoints[skillTypeB.id] ?? 0;

      if (skillA != skillB) {
        return skillB.compareTo(skillA); // 技能点高的优先
      }

      // 然后按难度排序（简单的优先）
      return a.difficulty.level.compareTo(b.difficulty.level);
    });

    return suitablePuzzles.take(limit).toList();
  }

  /// 获取下一个谜题
  Future<Puzzle?> getNextPuzzle({
    required String currentLevelId,
    required Map<String, int> userSkillPoints,
    required List<String> completedLevels,
  }) async {
    if (!_initialized) {
      _logger.w('PuzzleEngine not initialized');
      return null;
    }

    final currentPuzzle = _puzzleCache[currentLevelId];
    if (currentPuzzle == null) return null;

    // 首先尝试找同一世界的下一个谜题
    final worldId = currentPuzzle.themeWorld?.id ?? 'default';
    final worldPuzzles = _worldPuzzles[worldId] ?? [];

    final currentIndex = worldPuzzles.indexOf(currentLevelId);
    if (currentIndex >= 0 && currentIndex < worldPuzzles.length - 1) {
      final nextLevelId = worldPuzzles[currentIndex + 1];
      final nextPuzzle = _puzzleCache[nextLevelId];
      if (nextPuzzle != null && !completedLevels.contains(nextLevelId)) {
        return nextPuzzle;
      }
    }

    // 如果同一世界没有下一个谜题，推荐其他合适的谜题
    final recommended = await getRecommendedPuzzles(
      userSkillPoints: userSkillPoints,
      completedLevels: completedLevels,
      limit: 1,
    );

    return recommended.isNotEmpty ? recommended.first : null;
  }

  /// 获取指定世界的所有谜题
  List<Puzzle> getPuzzlesByWorld(String worldId) {
    if (!_initialized) {
      _logger.w('PuzzleEngine not initialized');
      return [];
    }

    final levelIds = _worldPuzzles[worldId] ?? [];
    return levelIds
        .map((id) => _puzzleCache[id])
        .where((puzzle) => puzzle != null)
        .cast<Puzzle>()
        .toList();
  }

  /// 获取指定类型的谜题
  List<Puzzle> getPuzzlesByType(PuzzleType type) {
    if (!_initialized) {
      _logger.w('PuzzleEngine not initialized');
      return [];
    }

    return _puzzleCache.values
        .where((puzzle) => puzzle.puzzleType == type)
        .toList();
  }

  /// 获取指定难度的谜题
  List<Puzzle> getPuzzlesByDifficulty(DifficultyLevel difficulty) {
    if (!_initialized) {
      _logger.w('PuzzleEngine not initialized');
      return [];
    }

    return _puzzleCache.values
        .where((puzzle) => puzzle.difficulty == difficulty)
        .toList();
  }

  /// 验证图形推理谜题答案
  bool validateGraphicPattern(Puzzle puzzle, String userAnswer) {
    try {
      final data = GraphicPatternData.fromJson(puzzle.data);
      return data.isCorrectAnswer(userAnswer);
    } catch (e) {
      _logger.e('Failed to validate graphic pattern answer', error: e);
      return false;
    }
  }

  /// 验证空间想象谜题答案
  bool validateSpatialVisualization(Puzzle puzzle, String userAnswer) {
    try {
      final data = SpatialVisualizationData.fromJson(puzzle.data);
      return data.isCorrectAnswer(userAnswer);
    } catch (e) {
      _logger.e('Failed to validate spatial visualization answer', error: e);
      return false;
    }
  }

  /// 验证数字逻辑谜题答案
  bool validateNumericLogic(Puzzle puzzle, List<String?> userGrid) {
    try {
      final data = NumericLogicData.fromJson(puzzle.data);

      // 创建一个新的数据对象来验证
      final testData = NumericLogicData(
        grid: userGrid,
        availableItems: data.availableItems,
        constraints: data.constraints,
      );

      return testData.isValidSolution();
    } catch (e) {
      _logger.e('Failed to validate numeric logic answer', error: e);
      return false;
    }
  }

  /// 验证编程启蒙谜题答案
  bool validateCoding(Puzzle puzzle, List<String> commands) {
    try {
      final data = CodingData.fromJson(puzzle.data);
      return data.validateCommandSequence(commands);
    } catch (e) {
      _logger.e('Failed to validate coding answer', error: e);
      return false;
    }
  }

  /// 验证镜像对称谜题答案
  bool validateMirrorSymmetry(Puzzle puzzle, String userAnswer) {
    try {
      final data = MirrorSymmetryData.fromJson(puzzle.data);
      return data.isCorrectAnswer(userAnswer);
    } catch (e) {
      _logger.e('Failed to validate mirror symmetry answer', error: e);
      return false;
    }
  }

  /// 通用答案验证
  bool validateAnswer(Puzzle puzzle, dynamic userAnswer) {
    switch (puzzle.puzzleType) {
      case PuzzleType.graphicPattern3x3:
        return validateGraphicPattern(puzzle, userAnswer as String);

      case PuzzleType.spatialVisualization:
        return validateSpatialVisualization(puzzle, userAnswer as String);

      case PuzzleType.numericLogic:
        return validateNumericLogic(puzzle, userAnswer as List<String?>);

      case PuzzleType.introToCoding:
        return validateCoding(puzzle, userAnswer as List<String>);

      case PuzzleType.mirrorSymmetry:
        return validateMirrorSymmetry(puzzle, userAnswer as String);
    }
  }

  /// 获取答案解析
  UniversalAnswerExplanation? getAnswerExplanation(
    Puzzle puzzle,
    dynamic userAnswer,
  ) {
    try {
      switch (puzzle.puzzleType) {
        case PuzzleType.graphicPattern3x3:
          return _getGraphicPatternExplanation(puzzle, userAnswer as String);

        case PuzzleType.spatialVisualization:
          return _getSpatialVisualizationExplanation(
            puzzle,
            userAnswer as String,
          );

        case PuzzleType.numericLogic:
          return _getNumericLogicExplanation(
            puzzle,
            userAnswer as List<String?>,
          );

        case PuzzleType.introToCoding:
          return _getCodingExplanation(puzzle, userAnswer as List<String>);

        case PuzzleType.mirrorSymmetry:
          return _getMirrorSymmetryExplanation(puzzle, userAnswer as String);
      }
    } catch (e) {
      _logger.e('Failed to get answer explanation', error: e);
      return null;
    }
  }

  /// 生成提示
  Map<String, dynamic> generateHint(Puzzle puzzle, {int hintLevel = 1}) {
    switch (puzzle.puzzleType) {
      case PuzzleType.graphicPattern3x3:
        return _generateGraphicPatternHint(puzzle, hintLevel);

      case PuzzleType.spatialVisualization:
        return _generateSpatialHint(puzzle, hintLevel);

      case PuzzleType.numericLogic:
        return _generateNumericLogicHint(puzzle, hintLevel);

      case PuzzleType.introToCoding:
        return _generateCodingHint(puzzle, hintLevel);

      case PuzzleType.mirrorSymmetry:
        return _generateMirrorSymmetryHint(puzzle, hintLevel);
    }
  }

  /// 生成图形推理提示
  Map<String, dynamic> _generateGraphicPatternHint(
    Puzzle puzzle,
    int hintLevel,
  ) {
    final data = GraphicPatternData.fromJson(puzzle.data);

    if (hintLevel == 1) {
      return {'type': 'text', 'content': '观察每一行和每一列的规律，图形是如何变化的？'};
    } else {
      // 二级提示：排除错误选项
      final wrongOptions = data.options
          .where((option) => option != data.answer)
          .toList();
      final excludeOption = wrongOptions[Random().nextInt(wrongOptions.length)];

      return {
        'type': 'exclude',
        'content': '这个选项是错误的',
        'excludeOption': excludeOption,
      };
    }
  }

  /// 生成空间想象提示
  Map<String, dynamic> _generateSpatialHint(Puzzle puzzle, int hintLevel) {
    if (hintLevel == 1) {
      return {'type': 'text', 'content': '想象一下如何将展开图折叠成立体图形，注意面与面的连接关系。'};
    } else {
      final data = SpatialVisualizationData.fromJson(puzzle.data);
      final wrongOptions = data.options
          .where((option) => option != data.answer)
          .toList();
      final excludeOption = wrongOptions[Random().nextInt(wrongOptions.length)];

      return {
        'type': 'exclude',
        'content': '这个立体图形是错误的',
        'excludeOption': excludeOption,
      };
    }
  }

  /// 生成数字逻辑提示
  Map<String, dynamic> _generateNumericLogicHint(Puzzle puzzle, int hintLevel) {
    if (hintLevel == 1) {
      return {'type': 'text', 'content': '记住：每一行、每一列、每个2×2的小方格内，四种图标都只能出现一次。'};
    } else {
      final data = NumericLogicData.fromJson(puzzle.data);
      final emptyIndices = data.emptyIndices;

      if (emptyIndices.isNotEmpty) {
        final hintIndex = emptyIndices[Random().nextInt(emptyIndices.length)];
        return {
          'type': 'highlight',
          'content': '试试从这个位置开始思考',
          'highlightIndex': hintIndex,
        };
      }

      return {'type': 'text', 'content': '检查是否有重复的图标在同一行、列或小方格中。'};
    }
  }

  /// 生成编程启蒙提示
  Map<String, dynamic> _generateCodingHint(Puzzle puzzle, int hintLevel) {
    if (hintLevel == 1) {
      return {'type': 'text', 'content': '想想小动物需要怎样移动才能到达宝物？先向哪个方向走？'};
    } else {
      final data = CodingData.fromJson(puzzle.data);
      final startX = data.startPosition['x']!;
      final startY = data.startPosition['y']!;
      final endX = data.endPosition['x']!;
      final endY = data.endPosition['y']!;

      String direction;
      if (endX > startX) {
        direction = '向右';
      } else if (endX < startX) {
        direction = '向左';
      } else if (endY > startY) {
        direction = '向下';
      } else {
        direction = '向上';
      }

      return {'type': 'text', 'content': '第一步可以尝试$direction移动'};
    }
  }

  /// 生成镜像对称提示
  Map<String, dynamic> _generateMirrorSymmetryHint(
    Puzzle puzzle,
    int hintLevel,
  ) {
    final data = MirrorSymmetryData.fromJson(puzzle.data);

    if (hintLevel == 1) {
      final directionText = data.mirrorDirection == 'horizontal' ? '左右' : '上下';
      return {
        'type': 'text',
        'content': '想象一下通过镜子看衣服，图案会$directionText对称地出现在相反的位置。',
      };
    } else {
      // 二级提示：排除错误选项或给出位置提示
      final wrongOptions = data.options
          .where((option) => option != data.answer)
          .toList();
      if (wrongOptions.isNotEmpty) {
        final excludeOption =
            wrongOptions[Random().nextInt(wrongOptions.length)];
        return {
          'type': 'exclude',
          'content': '这个选项是错误的',
          'excludeOption': excludeOption,
        };
      } else {
        return {
          'type': 'text',
          'content':
              '注意原始图案在${data.patternPosition}位置，镜像后应该在${data.expectedMirrorPosition}位置。',
        };
      }
    }
  }

  /// 获取推荐的下一个谜题
  String? getNextRecommendedPuzzle({
    required String currentLevelId,
    required Map<String, bool> completedLevels,
    required Map<String, int> recentPerformance,
  }) {
    final currentPuzzle = getPuzzle(currentLevelId);
    if (currentPuzzle == null) return null;

    final worldId = currentPuzzle.themeWorld?.id ?? 'default';
    final worldPuzzles = getPuzzlesByWorld(worldId);

    // 找到当前谜题在世界中的位置
    final currentIndex = worldPuzzles.indexWhere(
      (p) => p.levelId == currentLevelId,
    );
    if (currentIndex == -1) return null;

    // 计算最近表现的平均分
    final recentScores = recentPerformance.values.toList();
    final avgScore = recentScores.isEmpty
        ? 2.0
        : recentScores.reduce((a, b) => a + b) / recentScores.length;

    // 根据表现调整难度
    if (avgScore >= 2.5) {
      // 表现优秀，可以跳过一些简单关卡或尝试更难的
      for (int i = currentIndex + 1; i < worldPuzzles.length; i++) {
        final nextPuzzle = worldPuzzles[i];
        if (!completedLevels.containsKey(nextPuzzle.levelId)) {
          return nextPuzzle.levelId;
        }
      }
    } else if (avgScore < 1.5) {
      // 表现一般，推荐类似难度的练习
      final currentDifficulty = currentPuzzle.difficulty;
      final sameDifficultyPuzzles = getPuzzlesByDifficulty(
        currentDifficulty,
      ).where((p) => !completedLevels.containsKey(p.levelId)).toList();

      if (sameDifficultyPuzzles.isNotEmpty) {
        return sameDifficultyPuzzles.first.levelId;
      }
    }

    // 默认推荐下一个未完成的谜题
    for (int i = currentIndex + 1; i < worldPuzzles.length; i++) {
      final nextPuzzle = worldPuzzles[i];
      if (!completedLevels.containsKey(nextPuzzle.levelId)) {
        return nextPuzzle.levelId;
      }
    }

    return null;
  }

  /// 获取所有可用的世界
  List<String> getAvailableWorlds() {
    return _worldPuzzles.keys.toList();
  }

  /// 获取世界统计信息
  Map<String, dynamic> getWorldStats(String worldId) {
    final puzzles = getPuzzlesByWorld(worldId);

    final difficultyCount = <DifficultyLevel, int>{};
    final typeCount = <PuzzleType, int>{};

    for (final puzzle in puzzles) {
      difficultyCount[puzzle.difficulty] =
          (difficultyCount[puzzle.difficulty] ?? 0) + 1;
      typeCount[puzzle.puzzleType] = (typeCount[puzzle.puzzleType] ?? 0) + 1;
    }

    return {
      'totalPuzzles': puzzles.length,
      'difficultyDistribution': difficultyCount.map(
        (key, value) => MapEntry(key.displayName, value),
      ),
      'typeDistribution': typeCount.map(
        (key, value) => MapEntry(key.displayName, value),
      ),
      'estimatedTotalTime': puzzles
          .map((p) => p.estimatedTimeMinutes)
          .fold(0, (sum, time) => sum + time),
    };
  }

  /// 重新加载谜题（用于热更新）
  Future<void> reload() async {
    _puzzleCache.clear();
    _worldPuzzles.clear();
    _initialized = false;
    await initialize();
  }

  /// 获取引擎状态
  Map<String, dynamic> getEngineStatus() {
    return {
      'initialized': _initialized,
      'totalPuzzles': _puzzleCache.length,
      'totalWorlds': _worldPuzzles.length,
      'cacheSize': _puzzleCache.length,
      'worlds': _worldPuzzles.keys.toList(),
    };
  }

  // ==================== 答案解析私有方法 ====================

  /// 获取图形推理解析
  UniversalAnswerExplanation? _getGraphicPatternExplanation(
    Puzzle puzzle,
    String userAnswer,
  ) {
    try {
      final data = GraphicPatternData.fromJson(puzzle.data);

      // 如果数据中已有解析，优先使用
      if (data.explanation != null) {
        return data.explanation;
      }

      // 否则生成默认解析
      return AnswerExplanationFactory.createGraphicPatternExplanation(
        correctAnswer: data.answer,
        options: data.options,
        pattern: '图形变化规律', // 可以根据具体数据分析得出
      );
    } catch (e) {
      _logger.e('Failed to get graphic pattern explanation', error: e);
      return null;
    }
  }

  /// 获取空间想象解析
  UniversalAnswerExplanation? _getSpatialVisualizationExplanation(
    Puzzle puzzle,
    String userAnswer,
  ) {
    try {
      final data = SpatialVisualizationData.fromJson(puzzle.data);

      // 如果数据中已有解析，优先使用
      if (data.explanation != null) {
        return data.explanation;
      }

      // 否则生成默认解析
      return AnswerExplanationFactory.createSpatialVisualizationExplanation(
        correctAnswer: data.answer,
        options: data.options,
        expandedShape: data.expandedShape,
      );
    } catch (e) {
      _logger.e('Failed to get spatial visualization explanation', error: e);
      return null;
    }
  }

  /// 获取数字逻辑解析
  UniversalAnswerExplanation? _getNumericLogicExplanation(
    Puzzle puzzle,
    List<String?> userAnswer,
  ) {
    try {
      final data = NumericLogicData.fromJson(puzzle.data);

      // 如果数据中已有解析，优先使用
      if (data.explanation != null) {
        return data.explanation;
      }

      // 否则生成默认解析
      return AnswerExplanationFactory.createNumericLogicExplanation(
        constraints: data.constraints,
        availableItems: data.availableItems,
      );
    } catch (e) {
      _logger.e('Failed to get numeric logic explanation', error: e);
      return null;
    }
  }

  /// 获取编程启蒙解析
  UniversalAnswerExplanation? _getCodingExplanation(
    Puzzle puzzle,
    List<String> userAnswer,
  ) {
    try {
      final data = CodingData.fromJson(puzzle.data);

      // 如果数据中已有解析，优先使用
      if (data.explanation != null) {
        return data.explanation;
      }

      // 生成基于用户答案的解析
      return AnswerExplanationFactory.createCodingExplanation(
        solution: userAnswer,
        startPosition: data.startPosition,
        endPosition: data.endPosition,
      );
    } catch (e) {
      _logger.e('Failed to get coding explanation', error: e);
      return null;
    }
  }

  /// 获取镜像对称解析
  UniversalAnswerExplanation? _getMirrorSymmetryExplanation(
    Puzzle puzzle,
    String userAnswer,
  ) {
    try {
      final data = MirrorSymmetryData.fromJson(puzzle.data);

      // 优先使用有效的解析数据
      final effectiveExplanation = data.getEffectiveExplanation();
      if (effectiveExplanation != null) {
        return effectiveExplanation;
      }

      // 否则生成默认解析
      return AnswerExplanationFactory.createMirrorSymmetryExplanation(
        correctAnswer: data.answer,
        options: data.options,
        mirrorDirection: data.mirrorDirection,
        originalImage: data.originalImage,
      );
    } catch (e) {
      _logger.e('Failed to get mirror symmetry explanation', error: e);
      return null;
    }
  }
}
