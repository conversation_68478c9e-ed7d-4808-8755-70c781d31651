import 'package:flutter/material.dart';
import '../../core/theme/ux_theme_config.dart';

/// 镜像对称游戏组件
///
/// 这是一个完整的镜像对称游戏实现，包含：
/// - 原始衣服展示
/// - 镜子动画效果
/// - 选项展示和选择
/// - 答案验证和反馈
/// - 详细解析展示
class MirrorSymmetryWidget extends StatefulWidget {
  /// 题目数据
  final Map<String, dynamic> puzzleData;

  /// 答案选择回调
  final Function(String selectedAnswer) onAnswerSelected;

  /// 提示请求回调
  final VoidCallback? onHintRequested;

  /// 重新开始回调
  final VoidCallback? onRestart;

  const MirrorSymmetryWidget({
    super.key,
    required this.puzzleData,
    required this.onAnswerSelected,
    this.onHintRequested,
    this.onRestart,
  });

  @override
  State<MirrorSymmetryWidget> createState() => _MirrorSymmetryWidgetState();
}

class _MirrorSymmetryWidgetState extends State<MirrorSymmetryWidget>
    with TickerProviderStateMixin {
  // 动画控制器
  late AnimationController _mirrorAnimation;
  late AnimationController _selectionAnimation;

  // 游戏状态
  String? _selectedAnswer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _mirrorAnimation = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _selectionAnimation = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _mirrorAnimation.dispose();
    _selectionAnimation.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: EdgeInsets.all(UXThemeConfig.paddingM),
        child: Column(
          children: [
            // 镜像演示区域
            _buildMirrorDemonstration(),

            const SizedBox(height: 32),

            // 问题说明
            _buildQuestionText(),

            const SizedBox(height: 24),

            // 选项区域
            _buildOptionsArea(),
          ],
        ),
      ),
    );
  }

  Widget _buildMirrorDemonstration() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题
          Text(
            '镜像对称演示',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),

          const SizedBox(height: 20),

          // 主要演示区域
          SizedBox(
            height: 200,
            child: Row(
              children: [
                // 小亚手拿衣服
                Expanded(flex: 2, child: _buildPersonWithClothes()),

                // 镜子
                SizedBox(width: 80, child: _buildMirror()),

                // 镜像中的小亚
                Expanded(flex: 2, child: _buildMirrorReflection()),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // 说明文字
          Row(
            children: [
              Expanded(
                child: Text(
                  '手拿时\n图案在右边',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: UXThemeConfig.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 80),
              Expanded(
                child: Text(
                  '镜子中\n图案在左边',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: UXThemeConfig.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPersonWithClothes() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 小亚的头像
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.orange.shade200,
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.person, color: Colors.orange.shade700, size: 24),
          ),

          const SizedBox(height: 8),

          Text(
            '小亚',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),

          const SizedBox(height: 12),

          // 手拿的衣服
          _buildClothesInHand(),
        ],
      ),
    );
  }

  Widget _buildClothesInHand() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final originalPattern =
        data['originalPattern'] as Map<String, dynamic>? ?? {};

    return Container(
      width: 80,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 2),
      ),
      child: Stack(
        children: [
          // T恤轮廓
          Center(child: _buildTShirtShape()),
          // 图案
          _buildPatternOnClothes(originalPattern, false), // false表示不是镜像
        ],
      ),
    );
  }

  Widget _buildMirror() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 镜子框架
        AnimatedBuilder(
          animation: _mirrorAnimation,
          builder: (context, child) {
            return Container(
              width: 60,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.grey.shade300,
                    Colors.grey.shade100.withValues(
                      alpha: 0.8 + (_mirrorAnimation.value * 0.2),
                    ),
                    Colors.grey.shade300,
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade600, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 5,
                    offset: const Offset(2, 2),
                  ),
                ],
              ),
              child: Center(
                child: Icon(
                  Icons.crop_rotate,
                  color: Colors.grey.shade600,
                  size: 24,
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 8),

        Text(
          '镜子',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: UXThemeConfig.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildMirrorReflection() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 镜像中的小亚（翻转）
          Transform.scale(
            scaleX: -1, // 水平翻转
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.orange.shade200,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.person,
                color: Colors.orange.shade700,
                size: 24,
              ),
            ),
          ),

          const SizedBox(height: 8),

          Text(
            '镜像中的小亚',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: UXThemeConfig.textDark,
            ),
          ),

          const SizedBox(height: 12),

          // 镜像中的衣服
          _buildMirroredClothes(),
        ],
      ),
    );
  }

  Widget _buildMirroredClothes() {
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final originalPattern =
        data['originalPattern'] as Map<String, dynamic>? ?? {};

    return Container(
      width: 80,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 2),
      ),
      child: Stack(
        children: [
          // T恤轮廓
          Center(child: _buildTShirtShape()),
          // 镜像图案
          _buildPatternOnClothes(originalPattern, true), // true表示是镜像
        ],
      ),
    );
  }

  Widget _buildTShirtShape() {
    return Container(
      width: 80,
      height: 100,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: CustomPaint(painter: TShirtPainter(), size: const Size(80, 100)),
    );
  }

  Widget _buildPatternOnClothes(Map<String, dynamic> pattern, bool isMirrored) {
    final patternType = pattern['type'] as String? ?? 'heart';
    final position = pattern['position'] as String? ?? 'right';
    final color = pattern['color'] as String? ?? 'pink';

    // 如果是镜像，需要翻转位置
    String actualPosition = position;
    if (isMirrored) {
      switch (position) {
        case 'left':
          actualPosition = 'right';
          break;
        case 'right':
          actualPosition = 'left';
          break;
        case 'center':
        default:
          actualPosition = 'center';
          break;
      }
    }

    return _buildPatternWidget(patternType, actualPosition, color);
  }

  Widget _buildPatternWidget(String type, String position, String color) {
    Widget pattern;
    Color patternColor = _getColorFromString(color);

    switch (type) {
      case 'heart':
        pattern = Icon(Icons.favorite, color: patternColor, size: 24);
        break;
      case 'star':
        pattern = Icon(Icons.star, color: patternColor, size: 24);
        break;
      case 'bow':
        pattern = Icon(Icons.card_giftcard, color: patternColor, size: 24);
        break;
      case 'necklace':
        pattern = Icon(Icons.circle, color: patternColor, size: 20);
        break;
      default:
        pattern = Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            color: patternColor,
            shape: BoxShape.circle,
          ),
        );
    }

    // 根据位置确定对齐方式
    Alignment alignment;
    EdgeInsets padding;
    switch (position) {
      case 'left':
        alignment = Alignment.centerLeft;
        padding = const EdgeInsets.only(left: 12, top: 20, bottom: 20);
        break;
      case 'right':
        alignment = Alignment.centerRight;
        padding = const EdgeInsets.only(right: 12, top: 20, bottom: 20);
        break;
      case 'center':
      default:
        alignment = Alignment.center;
        padding = const EdgeInsets.only(top: 20, bottom: 20);
        break;
    }

    return Align(
      alignment: alignment,
      child: Padding(padding: padding, child: pattern),
    );
  }

  Color _getColorFromString(String colorName) {
    switch (colorName.toLowerCase()) {
      case 'pink':
        return Colors.pink;
      case 'blue':
        return Colors.blue;
      case 'yellow':
        return Colors.yellow;
      case 'green':
        return Colors.green;
      case 'red':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Widget _buildQuestionText() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: UXThemeConfig.accentBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: UXThemeConfig.accentBlue.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Text(
        '请问：以下A~D中，拿着看和透过镜子看，看到的是完全一样的是哪件？',
        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          color: UXThemeConfig.accentBlue,
          fontWeight: FontWeight.w600,
          height: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildOptionsArea() {
    // 从puzzleData的data字段中获取选项数据
    final data = widget.puzzleData['data'] as Map<String, dynamic>? ?? {};
    final options = data['options'] as List<dynamic>? ?? [];

    // 调试信息
    print('镜像对称游戏 - 选项数据: $options');
    print('镜像对称游戏 - 选项数量: ${options.length}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '请选择正确答案：',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: UXThemeConfig.textDark,
          ),
        ),
        const SizedBox(height: 16),

        // 如果没有选项，显示提示信息
        if (options.isEmpty)
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Text(
              '暂无选项数据，请检查谜题配置',
              style: TextStyle(
                color: Colors.orange.shade800,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          )
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            itemCount: options.length,
            itemBuilder: (context, index) {
              final optionId = options[index] as String;
              final isSelected = _selectedAnswer == optionId;

              print('镜像对称游戏 - 构建选项卡片: $optionId (索引: $index)');

              return _buildOptionCard(optionId, isSelected, index);
            },
          ),
      ],
    );
  }

  Widget _buildOptionCard(String optionId, bool isSelected, int index) {
    print('镜像对称游戏 - 构建选项卡片详情: $optionId, 选中: $isSelected, 索引: $index');

    return GestureDetector(
      onTap: () => _selectAnswer(optionId),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isSelected
              ? UXThemeConfig.primaryBlue.withValues(alpha: 0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(UXThemeConfig.radiusL),
          border: Border.all(
            color: isSelected
                ? UXThemeConfig.primaryBlue
                : UXThemeConfig.borderPrimary,
            width: isSelected ? 3 : 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            children: [
              // 选项标签
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: isSelected ? UXThemeConfig.primaryBlue : Colors.white,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? UXThemeConfig.primaryBlue
                        : Colors.grey.shade300,
                    width: 2,
                  ),
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + index), // A, B, C, D
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey.shade600,
                      fontWeight: FontWeight.bold,
                      fontSize: UXThemeConfig.fontSizeM,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 12),

              // 选项衣服
              Expanded(child: Center(child: _buildOptionClothes(optionId))),

              const SizedBox(height: 8),

              // 对称性说明
              Text(
                _getSymmetryDescription(optionId),
                style: TextStyle(
                  fontSize: 10,
                  color: UXThemeConfig.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),

              // 调试信息
              if (index < 2) // 只在前两个选项显示调试信息
                Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Text(
                    optionId,
                    style: TextStyle(
                      fontSize: 8,
                      color: Colors.red.shade400,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOptionClothes(String optionId) {
    // 解析选项ID，例如 "heart_left_blue"
    final parts = optionId.split('_');
    if (parts.length >= 3) {
      final type = parts[0];
      final position = parts[1];
      final color = parts[2];

      return Container(
        width: 80,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Stack(
          children: [
            // T恤形状背景
            Center(
              child: Container(
                width: 60,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: CustomPaint(
                  size: const Size(80, 100),
                  painter: TShirtPainter(),
                ),
              ),
            ),
            // 图案覆盖层
            Center(
              child: SizedBox(
                width: 80,
                height: 100,
                child: _buildPatternWidget(type, position, color),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Center(
        child: Text(
          '无效选项',
          style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
        ),
      ),
    );
  }

  String _getSymmetryDescription(String optionId) {
    final parts = optionId.split('_');
    if (parts.length >= 2) {
      final position = parts[1];
      switch (position) {
        case 'center':
          return '镜像对称';
        case 'left':
        case 'right':
          return '不对称';
        default:
          return '';
      }
    }
    return '';
  }

  void _selectAnswer(String optionId) {
    setState(() {
      _selectedAnswer = optionId;
    });

    _selectionAnimation.forward().then((_) {
      _selectionAnimation.reset();
    });

    // 调用回调
    widget.onAnswerSelected(optionId);
  }
}

/// T恤形状绘制器
class TShirtPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final outlinePaint = Paint()
      ..color = Colors.grey.shade400
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final fillPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final shadowPaint = Paint()
      ..color = Colors.grey.shade200
      ..style = PaintingStyle.fill;

    final path = Path();
    final shadowPath = Path();

    // 绘制T恤轮廓
    final width = size.width;
    final height = size.height;

    // 更真实的T恤形状
    // 左袖
    path.moveTo(width * 0.15, height * 0.2);
    path.lineTo(width * 0.3, height * 0.2);
    path.lineTo(width * 0.3, height * 0.12);
    // 领口（圆弧）
    path.quadraticBezierTo(
      width * 0.35,
      height * 0.08,
      width * 0.45,
      height * 0.08,
    );
    path.lineTo(width * 0.55, height * 0.08);
    path.quadraticBezierTo(
      width * 0.65,
      height * 0.08,
      width * 0.7,
      height * 0.12,
    );
    // 右袖
    path.lineTo(width * 0.7, height * 0.2);
    path.lineTo(width * 0.85, height * 0.2);
    path.lineTo(width * 0.85, height * 0.45);
    path.lineTo(width * 0.7, height * 0.45);
    // 右侧身
    path.lineTo(width * 0.7, height * 0.88);
    path.quadraticBezierTo(
      width * 0.68,
      height * 0.92,
      width * 0.65,
      height * 0.92,
    );
    // 下摆
    path.lineTo(width * 0.35, height * 0.92);
    path.quadraticBezierTo(
      width * 0.32,
      height * 0.92,
      width * 0.3,
      height * 0.88,
    );
    // 左侧身
    path.lineTo(width * 0.3, height * 0.45);
    path.lineTo(width * 0.15, height * 0.45);
    path.close();

    // 创建阴影路径（稍微偏移）
    shadowPath.addPath(path, const Offset(1, 1));

    // 绘制阴影
    canvas.drawPath(shadowPath, shadowPaint);
    // 绘制填充
    canvas.drawPath(path, fillPaint);
    // 绘制轮廓
    canvas.drawPath(path, outlinePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
