import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/bloc_exports.dart';
import '../../core/service_locator.dart';
import '../../domain/entities/user_profile_entity.dart';
import '../../domain/repositories/user_repository.dart';
import 'puzzle_game_page.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => getIt<UserBloc>()),
        BlocProvider(create: (context) => getIt<PuzzleBloc>()),
        BlocProvider(create: (context) => getIt<AchievementBloc>()),
      ],
      child: const HomeView(),
    );
  }
}

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  @override
  void initState() {
    super.initState();
    // 延迟加载数据，等待用户状态初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCurrentUser();
    });
  }

  void _loadCurrentUser() async {
    // 加载当前用户
    final userRepository = sl<UserRepository>();
    final currentUserResult = await userRepository.getCurrentUser();
    
    if (currentUserResult.isSuccess && currentUserResult.data != null) {
      final currentUser = currentUserResult.data!;
      // 设置当前用户到UserBloc
      context.read<UserBloc>().add(SelectUserEvent(currentUser.id));
      
      // 加载相关数据
      _loadUserData(currentUser.id);
    } else {
      // 如果没有当前用户，导航到用户选择页面
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/user_selection');
      }
    }
  }

  void _loadUserData(String userId) {
    // 加载推荐谜题
    context.read<PuzzleBloc>().add(LoadRecommendedPuzzlesEvent(userId));
    // 加载用户成就
    context.read<AchievementBloc>().add(LoadUserAchievementsEvent(userId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF6C5CE7),
              Color(0xFFA29BFE),
              Color(0xFFDDD6FE),
            ],
          ),
        ),
        child: SafeArea(
          child: CustomScrollView(
            slivers: [
              // 自定义应用栏
              _buildSliverAppBar(),
              
              // 主要内容
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 用户信息卡片
                      _buildUserInfoCard(),
                      const SizedBox(height: 24),
                      
                      // 快速开始区域
                      _buildQuickStartSection(),
                      const SizedBox(height: 24),
                      
                      // 世界地图导航
                      _buildWorldMapSection(),
                      const SizedBox(height: 24),
                      
                      // 进度概览
                      _buildProgressOverview(),
                      const SizedBox(height: 24),
                      
                      // 成就展示
                      _buildAchievementSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: true,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF6C5CE7),
                Color(0xFFA29BFE),
              ],
            ),
          ),
        ),
        title: const Text(
          '逻辑实验室',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.settings, color: Colors.white),
          onPressed: () {
            Navigator.of(context).pushNamed('/settings');
          },
        ),
        IconButton(
          icon: const Icon(Icons.person, color: Colors.white),
          onPressed: () {
            Navigator.of(context).pushNamed('/user_selection');
          },
        ),
      ],
    );
  }

  Widget _buildUserInfoCard() {
    return BlocBuilder<UserBloc, UserState>(
      builder: (context, state) {
        if (state is UserSelected) {
          return _buildUserInfoContent(state.user);
        }
        return _buildUserInfoPlaceholder();
      },
    );
  }

  Widget _buildUserInfoContent(UserProfileEntity user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // 用户头像
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _getAvatarColor(user.avatarId),
              boxShadow: [
                BoxShadow(
                  color: _getAvatarColor(user.avatarId).withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              _getAvatarIcon(user.avatarId),
              size: 30,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),
          
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '欢迎回来，${user.nickname}！',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D3436),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Lv.${user.currentLevel} • ${user.totalPoints} 技能点',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                
                // 等级进度条
                LinearProgressIndicator(
                  value: _calculateLevelProgress(user),
                  backgroundColor: Colors.grey.withValues(alpha: 0.2),
                  valueColor: AlwaysStoppedAnimation<Color>(_getAvatarColor(user.avatarId)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoPlaceholder() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: const Center(
        child: Text(
          '请先选择用户',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStartSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快速开始',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        
        BlocBuilder<PuzzleBloc, PuzzleState>(
                     builder: (context, state) {
             if (state is PuzzleListLoaded && state.puzzles.isNotEmpty) {
               final recommendedPuzzle = state.puzzles.first;
               return _buildQuickStartCard(
                 '推荐关卡',
                 recommendedPuzzle.prompt,
               );
             }
             return _buildQuickStartPlaceholder();
           },
        ),
      ],
    );
  }

  Widget _buildQuickStartCard(String title, String description) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed('/puzzle_game');
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF74B9FF), Color(0xFF0984E3)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF74B9FF).withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(15),
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 30,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStartPlaceholder() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: const Center(
        child: Text(
          '正在加载推荐关卡...',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  Widget _buildWorldMapSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '探索世界',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        
        Container(
          height: 200,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // 地图背景
                Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF00B894), Color(0xFF00CEC9)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                ),
                
                // 世界主题按钮
                Positioned(
                  top: 20,
                  left: 20,
                  child: _buildWorldThemeButton('图形推理', Icons.extension, const Color(0xFF6C5CE7)),
                ),
                Positioned(
                  top: 20,
                  right: 20,
                  child: _buildWorldThemeButton('空间想象', Icons.view_in_ar, const Color(0xFF74B9FF)),
                ),
                Positioned(
                  bottom: 20,
                  left: 20,
                  child: _buildWorldThemeButton('数字逻辑', Icons.calculate, const Color(0xFFE17055)),
                ),
                Positioned(
                  bottom: 20,
                  right: 20,
                  child: _buildWorldThemeButton('编程启蒙', Icons.code, const Color(0xFFE84393)),
                ),
                
                // 新增：镜像对称按钮
                Positioned(
                  top: 80,
                  left: 60,
                  child: _buildWorldThemeButton('镜像对称', Icons.flip, const Color(0xFF00CEC9)),
                ),
                
                // 中心探索按钮
                Center(
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pushNamed('/world_map');
                    },
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.explore,
                        size: 40,
                        color: Color(0xFF2D3436),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWorldThemeButton(String title, IconData icon, Color color) {
    return GestureDetector(
      onTap: () {
        final userState = context.read<UserBloc>().state;
        if (userState is UserSelected) {
          // 根据不同游戏类型导航到对应的谜题
          String levelId;
          switch (title) {
            case '图形推理':
              levelId = 'graphic_pattern_sample';
              break;
            case '空间想象':
              levelId = 'spatial_visualization_sample';
              break;
            case '数字逻辑':
              levelId = 'numeric_logic_sample';
              break;
            case '编程启蒙':
              levelId = 'coding_sample';
              break;
            case '镜像对称':
              levelId = 'mirror_symmetry_sample';
              break;
            default:
              // 其他类型导航到谜题列表
              Navigator.of(context).pushNamed('/puzzle_list', arguments: title);
              return;
          }
          
          // 直接进入对应的游戏
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => PuzzleGamePage(
                levelId: levelId,
                userId: userState.user.id,
              ),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('请先选择用户')),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 24),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressOverview() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '今日进度',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: _buildProgressCard(
                '游戏时长',
                '25分钟',
                Icons.timer,
                const Color(0xFFFDCB6E),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildProgressCard(
                '完成关卡',
                '3个',
                Icons.check_circle,
                const Color(0xFF00B894),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildProgressCard(
                '新成就',
                '1个',
                Icons.star,
                const Color(0xFFE84393),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProgressCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 30),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '最新成就',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pushNamed('/achievements');
              },
              child: const Text(
                '查看全部',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
                 BlocBuilder<AchievementBloc, AchievementState>(
           builder: (context, state) {
             if (state is AchievementsLoaded && state.unlockedAchievements.isNotEmpty) {
               return SizedBox(
                 height: 120,
                 child: ListView.builder(
                   scrollDirection: Axis.horizontal,
                   itemCount: state.unlockedAchievements.length.clamp(0, 3),
                   itemBuilder: (context, index) {
                     final achievement = state.unlockedAchievements[index];
                     return _buildAchievementCard(achievement.title, achievement.description);
                   },
                 ),
               );
             }
             return _buildAchievementPlaceholder();
           },
         ),
      ],
    );
  }

  Widget _buildAchievementCard(String title, String description) {
    return Container(
      width: 200,
      margin: const EdgeInsets.only(right: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFD79A8), Color(0xFFE84393)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFD79A8).withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.emoji_events,
            color: Colors.white,
            size: 30,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.9),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildAchievementPlaceholder() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: const Center(
        child: Text(
          '暂无成就',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  Color _getAvatarColor(String avatarId) {
    switch (avatarId) {
      case 'avatar_1':
        return const Color(0xFF6C5CE7);
      case 'avatar_2':
        return const Color(0xFFA29BFE);
      case 'avatar_3':
        return const Color(0xFF74B9FF);
      case 'avatar_4':
        return const Color(0xFF00B894);
      case 'avatar_5':
        return const Color(0xFFE17055);
      case 'avatar_6':
        return const Color(0xFFE84393);
      case 'avatar_7':
        return const Color(0xFFFD79A8);
      case 'avatar_8':
        return const Color(0xFFFDCB6E);
      case 'avatar_9':
        return const Color(0xFF6C5CE7);
      case 'avatar_10':
        return const Color(0xFF00CEC9);
      case 'avatar_11':
        return const Color(0xFFB2BEC3);
      case 'avatar_12':
        return const Color(0xFF636E72);
      default:
        return const Color(0xFF6C5CE7);
    }
  }

  IconData _getAvatarIcon(String avatarId) {
    switch (avatarId) {
      case 'avatar_1':
        return Icons.child_care;
      case 'avatar_2':
        return Icons.face;
      case 'avatar_3':
        return Icons.pets;
      case 'avatar_4':
        return Icons.sports_esports;
      case 'avatar_5':
        return Icons.school;
      case 'avatar_6':
        return Icons.star;
      case 'avatar_7':
        return Icons.favorite;
      case 'avatar_8':
        return Icons.wb_sunny;
      case 'avatar_9':
        return Icons.rocket_launch;
      case 'avatar_10':
        return Icons.palette;
      case 'avatar_11':
        return Icons.music_note;
      case 'avatar_12':
        return Icons.sports_soccer;
      default:
        return Icons.person;
    }
  }

  double _calculateLevelProgress(UserProfileEntity user) {
    // 简单的等级进度计算逻辑
    int pointsForCurrentLevel = (user.currentLevel - 1) * 100;
    int pointsForNextLevel = user.currentLevel * 100;
    int currentLevelPoints = user.totalPoints - pointsForCurrentLevel;
    
    if (currentLevelPoints <= 0) return 0.0;
    if (user.totalPoints >= pointsForNextLevel) return 1.0;
    
    return currentLevelPoints / 100.0;
  }
} 