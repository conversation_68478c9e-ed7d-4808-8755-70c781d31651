import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../bloc/puzzle/puzzle_bloc.dart';
import '../widgets/mirror_symmetry_widget.dart';
import '../../domain/entities/puzzle_entity.dart';

/// 直接测试镜像对称游戏的页面
class MirrorSymmetryTestDirectPage extends StatefulWidget {
  const MirrorSymmetryTestDirectPage({super.key});

  @override
  State<MirrorSymmetryTestDirectPage> createState() => _MirrorSymmetryTestDirectPageState();
}

class _MirrorSymmetryTestDirectPageState extends State<MirrorSymmetryTestDirectPage> {
  String? _selectedAnswer;
  bool _isSubmitted = false;
  
  // 测试数据
  final Map<String, dynamic> _testPuzzleData = {
    'data': {
      'originalPattern': {
        'type': 'heart',
        'position': 'right',
        'color': 'pink'
      },
      'options': [
        'blue_tshirt_heart_center_bow',
        'yellow_tshirt_heart_left_necklace', 
        'pink_tshirt_bow_right_necklace',
        'green_tshirt_heart_center'
      ],
      'correctAnswer': 'green_tshirt_heart_center',
      'explanation': '在镜像对称中,只有位于中心的图案在镜子中看起来是完全一样的。选项D中的心形位于T恤的中心,因此在镜子中看起来是完全一样的。'
    }
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('镜像对称游戏测试'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        actions: [
          // 时间显示测试
          Container(
            margin: const EdgeInsets.only(right: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.timer, size: 16),
                SizedBox(width: 4),
                Text('01:23', style: TextStyle(fontWeight: FontWeight.bold)),
              ],
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 测试说明
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🧪 测试说明',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '这是镜像对称游戏的直接测试页面。请测试以下功能：\n'
                    '1. 选项是否可以点击\n'
                    '2. T恤和图案显示是否正常\n'
                    '3. 镜像效果是否正确',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.blue.shade700,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // 镜像对称游戏组件
            MirrorSymmetryWidget(
              puzzleData: _testPuzzleData,
              onAnswerSelected: (selectedAnswer) {
                setState(() {
                  _selectedAnswer = selectedAnswer;
                  _isSubmitted = true;
                });
                
                // 显示结果
                _showResult(selectedAnswer);
              },
              onHintRequested: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('提示功能测试 - 功能正常')),
                );
              },
              onRestart: () {
                setState(() {
                  _selectedAnswer = null;
                  _isSubmitted = false;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('重新开始测试')),
                );
              },
            ),
            
            const SizedBox(height: 24),
            
            // 测试结果显示
            if (_isSubmitted) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: _selectedAnswer == _testPuzzleData['data']['correctAnswer']
                      ? Colors.green.shade50
                      : Colors.red.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _selectedAnswer == _testPuzzleData['data']['correctAnswer']
                        ? Colors.green.shade200
                        : Colors.red.shade200,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _selectedAnswer == _testPuzzleData['data']['correctAnswer']
                              ? Icons.check_circle
                              : Icons.error,
                          color: _selectedAnswer == _testPuzzleData['data']['correctAnswer']
                              ? Colors.green.shade600
                              : Colors.red.shade600,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _selectedAnswer == _testPuzzleData['data']['correctAnswer']
                              ? '✅ 测试通过！'
                              : '❌ 测试失败',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _selectedAnswer == _testPuzzleData['data']['correctAnswer']
                                ? Colors.green.shade800
                                : Colors.red.shade800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '选择的答案: $_selectedAnswer\n'
                      '正确答案: ${_testPuzzleData['data']['correctAnswer']}\n'
                      '选项点击功能: ✅ 正常工作',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  void _showResult(String selectedAnswer) {
    final isCorrect = selectedAnswer == _testPuzzleData['data']['correctAnswer'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              isCorrect ? Icons.check_circle : Icons.error,
              color: isCorrect ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 8),
            Text(isCorrect ? '正确！' : '错误'),
          ],
        ),
        content: Text(
          isCorrect 
              ? '恭喜！您选择了正确答案。\n\n${_testPuzzleData['data']['explanation']}'
              : '很遗憾，答案不正确。\n\n正确答案是: ${_testPuzzleData['data']['correctAnswer']}\n\n${_testPuzzleData['data']['explanation']}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
